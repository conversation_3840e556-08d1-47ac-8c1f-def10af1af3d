'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  AlertCircle,
  RefreshCw,
  Info,
  Bug,
  Server,
  Shield,
  Cpu,
  Clock,
  LogIn,
  AlertTriangle,
  Database,
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToastHelpers } from '@/lib/ToastContext';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';

interface SystemLog {
  _id: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  source: string;
  details?: string;
  timestamp: string;
}

interface SystemLogsProps {
  limit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
  height?: string;
}

export default function SystemLogs({
  limit = 20,
  autoRefresh: initialAutoRefresh = false,
  refreshInterval: initialRefreshInterval = 60000, // 60 seconds default
  height = "500px"
}: SystemLogsProps) {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [logLevel, setLogLevel] = useState<string>('all');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(initialAutoRefresh);
  const [refreshInterval, setRefreshInterval] = useState(initialRefreshInterval);
  const [sessionFetchCount, setSessionFetchCount] = useState(0);
  const maxFetchesPerSession = 50; // Increased limit for fetches per session
  const sessionTimeout = useRef<NodeJS.Timeout | null>(null);

  // Get auth context for user ID
  const { user, isAdmin } = useAuth();

  const toast = useToastHelpers();
  const retryCount = useRef(0);
  const maxRetries = 2; // Maximum number of automatic retries
  const toastShown = useRef(false);
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const autoRefreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch system logs with debouncing
  const fetchLogs = useCallback(async (silent = false) => {
    // Check if we've exceeded our fetch limit for this session
    if (sessionFetchCount >= maxFetchesPerSession) {
      console.warn(`Fetch limit (${maxFetchesPerSession}) reached for this session. Wait or reload the page.`);
      // Only show the warning toast once and only if not in silent mode
      if (!silent && !toastShown.current) {
        toast.warning('Warning', 'Too many refresh attempts. Please wait a few minutes or reload the page.');
        toastShown.current = true;
      }
      return;
    }

    // Start session timeout reset if not already running
    if (!sessionTimeout.current) {
      // Reset fetch count after 10 minutes of inactivity (increased from 5 minutes)
      sessionTimeout.current = setTimeout(() => {
        setSessionFetchCount(0);
        toastShown.current = false;
        sessionTimeout.current = null;
      }, 10 * 60 * 1000);
    }

    // Debounce the fetch request
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(async () => {
      // Clear any existing timeout
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
        fetchTimeoutRef.current = null;
      }

      if (!silent) {
        setIsLoading(true);
      }
      setError(null);

      try {
        // Check if user is authenticated and is admin
        if (!user || !user.id) {
          throw new Error('Authentication required. Please sign in again.');
        }

        if (!isAdmin()) {
          throw new Error('You do not have permission to view system logs.');
        }

        // Construct URL with optional filters
        const url = new URL('/api/admin/system/logs', window.location.origin);
        url.searchParams.append('limit', limit.toString());
        url.searchParams.append('userId', user.id); // Add userId to query params

        if (logLevel !== 'all') {
          url.searchParams.append('level', logLevel);
        }

        // Add cache busting parameter only when manually refreshing
        if (!autoRefresh) {
          url.searchParams.append('_t', Date.now().toString());
        }

        const fetchOptions = {
          credentials: 'include' as RequestCredentials,
          cache: 'no-store' as RequestCache
        };

        const response = await fetch(url.toString(), fetchOptions);

        if (!response.ok) {
          const errorData = await response.json();

          // Handle authentication errors specifically
          if (response.status === 401) {
            console.error('Authentication required for system logs');
            throw new Error('Authentication required. Please sign in again.');
          } else if (response.status === 403) {
            console.error('Forbidden: User does not have admin permissions');
            throw new Error('You do not have permission to view system logs.');
          }

          throw new Error(errorData.error || `Failed to fetch logs (${response.status})`);
        }

        const data = await response.json();

        // If we have no logs and the backend returned an empty array,
        // create some sample logs for demonstration when in development
        if (data.logs.length === 0 && process.env.NODE_ENV === 'development') {
          // Generate sample logs data for development/testing
          const sampleLogs = generateSampleLogs(10);
          setLogs(sampleLogs);
        } else {
          setLogs(data.logs || []);
        }

        setLastRefresh(new Date());
        retryCount.current = 0; // Reset retry count on success
        toastShown.current = false; // Reset toast flag on success
        setSessionFetchCount(prevCount => prevCount + 1); // Increment fetch count
      } catch (error) {
        console.error('Error fetching system logs:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
        setError(errorMessage);

        // Only show toast once per error session and only for critical errors
        if (!toastShown.current && !silent && retryCount.current >= maxRetries) {
          toast.error('Error', 'Failed to fetch system logs');
          toastShown.current = true;
        }

        // If in development mode and we have an error, generate sample logs
        if (process.env.NODE_ENV === 'development') {
          const sampleLogs = generateSampleLogs(10);
          setLogs(sampleLogs);
        }

        // Retry logic with exponential backoff
        if (retryCount.current < maxRetries) {
          const delay = Math.pow(2, retryCount.current) * 1000; // 1s, 2s, 4s...
          retryCount.current++;

          // Schedule retry
          fetchTimeoutRef.current = setTimeout(() => {
            fetchLogs(true); // Retry silently
          }, delay);
        }
      } finally {
        setIsLoading(false);
      }
    }, 300); // 300ms debounce
  }, [limit, logLevel, toast, autoRefresh, sessionFetchCount, maxFetchesPerSession, user, isAdmin]);

  // Create a stable fetch function that we can use in the useEffect
  const stableFetchLogs = useCallback(() => {
    fetchLogs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Create a stable silent fetch function for auto-refresh
  const stableSilentFetchLogs = useCallback(() => {
    fetchLogs(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Generate sample logs for development/testing
  const generateSampleLogs = (count: number): SystemLog[] => {
    const sources = ['server', 'auth', 'system', 'database', 'api'];
    const levels = ['info', 'warning', 'error', 'debug'];
    const messages = [
      'User login successful',
      'Failed authentication attempt',
      'Database connection established',
      'System startup complete',
      'High CPU usage detected',
      'Low memory warning',
      'API rate limit exceeded',
      'Security scan completed',
      'Scheduled maintenance started',
      'Backup process completed',
    ];

    return Array.from({ length: count }).map((_, index) => {
      const level = levels[Math.floor(Math.random() * levels.length)] as 'info' | 'warning' | 'error' | 'debug';
      const source = sources[Math.floor(Math.random() * sources.length)];
      const message = messages[Math.floor(Math.random() * messages.length)];
      const date = new Date();
      date.setMinutes(date.getMinutes() - Math.floor(Math.random() * 60));

      return {
        _id: `sample-${index}`,
        level,
        source,
        message,
        details: level === 'error' ? 'Error details: ' + Math.random().toString(36).substring(2, 15) : undefined,
        timestamp: date.toISOString(),
      };
    });
  };

  // Handle auto-refresh
  useEffect(() => {
    // Clear any existing auto-refresh timeout
    if (autoRefreshTimeoutRef.current) {
      clearTimeout(autoRefreshTimeoutRef.current);
      autoRefreshTimeoutRef.current = null;
    }

    // Set up auto-refresh if enabled
    if (autoRefresh && refreshInterval > 0) {
      autoRefreshTimeoutRef.current = setTimeout(() => {
        stableSilentFetchLogs(); // Use the stable silent fetch function
      }, refreshInterval);
    }

    return () => {
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }
    };
  }, [autoRefresh, refreshInterval, stableSilentFetchLogs]);

  // Fetch logs on component mount and when filters change
  useEffect(() => {
    // Only fetch logs if user is loaded and is admin
    if (user && isAdmin()) {
      stableFetchLogs();
    } else if (user && !isAdmin()) {
      // If user is loaded but not admin, show error
      setError('You do not have permission to view system logs.');
      setIsLoading(false);
    } else if (!user) {
      // If user is not loaded yet, show loading state
      setIsLoading(true);
    }

    // Cleanup on unmount
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (sessionTimeout.current) {
        clearTimeout(sessionTimeout.current);
      }
    };
  // Only run this effect when these dependencies change, not on every render
  }, [user, isAdmin, logLevel, limit, stableFetchLogs]);

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (e) {
      return 'Unknown time';
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Get icon for log level
  const getLogIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <AlertTriangle className="h-4 w-4" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4" />;
      case 'info':
        return <Info className="h-4 w-4" />;
      case 'debug':
        return <Bug className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  // Get icon for log source
  const getSourceIcon = (source: string) => {
    switch (source.toLowerCase()) {
      case 'server':
        return <Server className="h-4 w-4" />;
      case 'auth':
        return <Shield className="h-4 w-4" />;
      case 'system':
        return <Cpu className="h-4 w-4" />;
      case 'database':
        return <Database className="h-4 w-4" />;
      default:
        return <Server className="h-4 w-4" />;
    }
  };

  // Get badge variant for log level
  const getLogLevelVariant = (level: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (level) {
      case 'error':
        return 'destructive';
      case 'warning':
        return 'default';
      case 'info':
        return 'secondary';
      case 'debug':
        return 'outline';
      default:
        return 'outline';
    }
  };

  // Get logs for display
  const getDisplayLogs = () => {
    if (logs.length > 0) return logs;

    if (error && !isLoading) {
      // Return empty array to show "No system logs found" message
      return [];
    }

    return [];
  };

  const displayLogs = getDisplayLogs();

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-3 flex-wrap gap-2">
        <div>
          <CardTitle className="text-vista-light">System Logs</CardTitle>
          <CardDescription>
            Server and system-level events
          </CardDescription>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-refresh"
              checked={autoRefresh}
              onCheckedChange={setAutoRefresh}
            />
            <Label htmlFor="auto-refresh" className="flex items-center cursor-pointer">
              <Clock className="h-4 w-4 mr-1" />
              <span className="text-xs">Auto-refresh</span>
            </Label>
          </div>

          <Select value={logLevel} onValueChange={setLogLevel}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="All Levels" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value="error">Errors</SelectItem>
              <SelectItem value="warning">Warnings</SelectItem>
              <SelectItem value="info">Info</SelectItem>
              <SelectItem value="debug">Debug</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={() => {
            retryCount.current = 0; // Reset retry count on manual refresh
            toastShown.current = false; // Reset toast flag
            fetchLogs();
          }} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {error && !isLoading && logs.length === 0 ? (
          <div className="p-4 mb-4 border rounded-md bg-red-900/20 border-red-800 text-red-100">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <span className="font-medium">Error loading logs</span>
            </div>
            <p className="text-sm">{error}</p>
          </div>
        ) : (
          <div className="relative border rounded-md overflow-hidden">
            {/* Mobile-optimized layout */}
            <div className="block md:hidden">
              <div
                className="custom-scrollbar overflow-auto admin-scroll-mobile"
                style={{ height, overflowX: "hidden" }}
              >
                <div className="space-y-3 p-4">
                  {isLoading ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <div key={index} className="admin-card-mobile">
                        <Skeleton className="h-4 w-20 mb-2" />
                        <Skeleton className="h-4 w-full mb-1" />
                        <Skeleton className="h-3 w-3/4" />
                      </div>
                    ))
                  ) : displayLogs.length > 0 ? (
                    displayLogs.map((log) => (
                      <div
                        key={log._id}
                        className={`admin-card-mobile ${log.level === 'error' ? 'border-red-500/30 bg-red-900/10' : ''}`}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <Badge
                            variant={getLogLevelVariant(log.level)}
                            className="flex items-center gap-1"
                          >
                            {getLogIcon(log.level)}
                            {log.level}
                          </Badge>
                          <span className="text-xs text-vista-light/50">
                            {formatRelativeTime(log.timestamp)}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 mb-2">
                          {getSourceIcon(log.source)}
                          <span className="text-sm font-medium text-vista-light">{log.source}</span>
                        </div>
                        <p className="text-sm text-vista-light/90 break-words">{log.message}</p>
                        {log.details && (
                          <details className="mt-2">
                            <summary className="text-xs text-vista-blue cursor-pointer hover:text-vista-blue/80">
                              View Details
                            </summary>
                            <pre className="text-xs text-vista-light/70 mt-1 whitespace-pre-wrap break-words bg-vista-dark/30 p-2 rounded">
                              {log.details}
                            </pre>
                          </details>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-vista-light/50">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No logs available</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Desktop table layout */}
            <div className="hidden md:block">
              <div
                className="custom-scrollbar overflow-auto"
                style={{ height, overflowX: "auto" }}
              >
                <table className="w-full border-collapse">
                  <thead className="sticky top-0 bg-background z-10">
                    <tr>
                      <th className="w-36 text-vista-light/70 text-left p-3 border-b border-vista-light/10">Time</th>
                    <th className="w-28 text-vista-light/70 text-left p-3 border-b border-vista-light/10">Level</th>
                    <th className="w-32 text-vista-light/70 text-left p-3 border-b border-vista-light/10">Source</th>
                    <th className="text-vista-light/70 text-left p-3 border-b border-vista-light/10">Message</th>
                  </tr>
                </thead>
                <tbody>
                  {isLoading && displayLogs.length === 0 ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <tr key={index}>
                        <td className="p-3 border-b border-vista-light/5"><Skeleton className="h-4 w-24" /></td>
                        <td className="p-3 border-b border-vista-light/5"><Skeleton className="h-4 w-20" /></td>
                        <td className="p-3 border-b border-vista-light/5"><Skeleton className="h-4 w-20" /></td>
                        <td className="p-3 border-b border-vista-light/5"><Skeleton className="h-4 w-full" /></td>
                      </tr>
                    ))
                  ) : displayLogs.length > 0 ? (
                    displayLogs.map((log) => (
                      <tr 
                        key={log._id} 
                        className={`hover:bg-vista-light/5 ${log.level === 'error' ? 'bg-red-900/10' : ''}`}
                      >
                        <td className="whitespace-nowrap p-3 border-b border-vista-light/5">
                          <span
                            className="text-xs text-vista-light/70"
                            title={formatDate(log.timestamp)}
                          >
                            {formatRelativeTime(log.timestamp)}
                          </span>
                        </td>

                        <td className="p-3 border-b border-vista-light/5">
                          <Badge
                            variant={getLogLevelVariant(log.level)}
                            className="flex items-center gap-1"
                          >
                            {getLogIcon(log.level)}
                            {log.level}
                          </Badge>
                        </td>

                        <td className="p-3 border-b border-vista-light/5">
                          <div className="flex items-center gap-1">
                            {getSourceIcon(log.source)}
                            <span className="text-sm">{log.source}</span>
                          </div>
                        </td>

                        <td className="p-3 border-b border-vista-light/5">
                          <div className="font-medium text-vista-light/90">{log.message}</div>
                          {log.details && (
                            <div className="text-xs text-vista-light/60 mt-1 bg-black/20 p-1.5 rounded-sm">
                              {(() => {
                                try {
                                  return typeof log.details === 'string' && log.details.startsWith('{') 
                                    ? JSON.stringify(JSON.parse(log.details), null, 2)
                                    : log.details;
                                } catch (e) {
                                  return log.details;
                                }
                              })()}
                            </div>
                          )}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={4} className="text-center py-12">
                        <div className="flex flex-col items-center justify-center">
                          <AlertCircle className="h-8 w-8 text-vista-light/30 mb-2" />
                          <span className="text-vista-light/50">No system logs found</span>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    )}
        <div className="text-xs text-vista-light/50 mt-2 text-right">
          Last updated: {lastRefresh.toLocaleTimeString()}
          {autoRefresh && (
            <span className="ml-2">
              (Auto-refresh {Math.round(refreshInterval / 1000)}s)
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
