'use client';

import { AdminSidebar } from '@/components/admin/AdminSidebar';
import { AuthProvider } from '@/contexts/AuthContext';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { AlertCircle, Loader2 } from 'lucide-react';
import '../globals.css';

// Admin layout wrapper with client-side protection
function AdminLayoutWrapper({ children }: { children: React.ReactNode }) {
  const { user, isAdmin, isLoading, verifyAdminStatus } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [authError, setAuthError] = useState<string | null>(null);

  useEffect(() => {
    // If authentication is still loading, wait
    if (isLoading) return;

    // Check admin status with both client and server
    const checkAdminStatus = async () => {
      // Check if user exists
      if (!user) {
        console.log('No user found in auth context');
        setAuthError('No user found. Please sign in.');
        setIsCheckingAuth(false);
        router.push('/');
        return;
      }

      // Check client-side admin status
      if (!isAdmin()) {
        console.log('User is not an admin according to client-side check');
        setAuthError('You do not have admin privileges.');
        setIsCheckingAuth(false);
        router.push('/');
        return;
      }

      // Verify admin status with the server
      try {
        console.log('Verifying admin status with server for user:', user.id);
        const isServerAdmin = await verifyAdminStatus();

        if (!isServerAdmin) {
          console.error('Server rejected admin status for user:', user.id);
          setAuthError('Server verification failed. You do not have admin privileges.');
          setIsCheckingAuth(false);
          router.push('/');
          return;
        }

        console.log('Server confirmed admin status for user:', user.id);

        // User is authenticated and is an admin on both client and server
        setIsAuthorized(true);
        setIsCheckingAuth(false);
      } catch (error) {
        console.error('Error verifying admin status:', error);
        setAuthError('Error verifying admin status. Please try again.');
        setIsCheckingAuth(false);
        router.push('/');
      }
    };

    checkAdminStatus();
  }, [user, isAdmin, isLoading, router, verifyAdminStatus]);

  // Show loading state while checking authentication
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-vista-blue" />
          <p className="text-vista-light">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  // Show unauthorized message if not authorized
  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="text-center max-w-md p-6">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-vista-light mb-2">Access Denied</h2>
          <p className="text-vista-light/70 mb-4">
            {authError || 'You do not have permission to access the admin area. This area is restricted to admin users only.'}
          </p>
          <button
            onClick={() => router.push('/')}
            className="px-4 py-2 bg-vista-blue/20 hover:bg-vista-blue/30 text-vista-blue rounded-md transition-colors"
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }

  // Render admin layout if authorized
  return (
    <div className="min-h-screen bg-vista-dark">
      <AdminSidebar />
      <main className="min-h-screen transition-all duration-300 ease-in-out
                     md:ml-64
                     flex flex-col">
        {/* Mobile-optimized content container */}
        <div className="flex-1 p-3 sm:p-4 md:p-6 pt-16 md:pt-6
                       max-w-full overflow-x-hidden">
          <div className="w-full max-w-7xl mx-auto space-y-4 md:space-y-6">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}

// Main layout component
export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AdminLayoutWrapper>
      {children}
    </AdminLayoutWrapper>
  );
}
