import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { withMongo } from '@/middleware/mongoMiddleware';
import { isAdmin } from '@/lib/auth';
import mongoose from 'mongoose';

// Define interfaces for our schemas
interface IUserActivity {
  userId: mongoose.Types.ObjectId;
  type: string;
  action: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

interface ISystemLog {
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  source: string;
  timestamp: Date;
  details?: Record<string, unknown>;
}

// Interface for database document with _id
interface ISystemLogDocument extends ISystemLog {
  _id: mongoose.Types.ObjectId;
  __v?: number;
}

interface IUser {
  _id: string;
  role: string;
}

// Set cache control headers to reduce excessive calls
const setCacheHeaders = (response: NextResponse) => {
  // Cache for 10 seconds only to ensure fresh data
  response.headers.set('Cache-Control', 'public, max-age=10');
  return response;
};

/**
 * GET /api/admin/system/logs
 * Get system logs with pagination and filtering
 */
async function getHandler(request: NextRequest) {
  try {
    // Parse URL and get search parameters
    const url = new URL(request.url);
    const searchParams = url.searchParams;
    
    // Get userId from query parameter as a fallback
    const queryUserId = searchParams.get('userId');

    // Try to get userId from cookie first, then fall back to query parameter
    let adminCheck;

    // If userId is provided in query, use it to check admin status
    if (queryUserId) {
      // Get the User model
      const User = mongoose.models.User ||
                  mongoose.model('User', new mongoose.Schema({
                    role: String
                  }));

      // Find the user and check if they're an admin
      const user = await User.findById(queryUserId).select('role').lean() as IUser | null;

      if (!user) {
        return NextResponse.json({ error: "User not found" }, { status: 401 });
      }

      if ((user.role !== 'admin') && (user.role !== 'superadmin')) {
        return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource" }, { status: 403 });
      }

      // User is an admin
      adminCheck = { isAuthorized: true, userId: queryUserId };
    } else {
      // Fall back to cookie-based auth
      adminCheck = await isAdmin(request);
      if (!adminCheck.isAuthorized) {
        return NextResponse.json({ error: adminCheck.message || "Unauthorized" }, { status: adminCheck.status || 401 });
      }
    }

    // User is authenticated and is an admin
    const userId = adminCheck.userId;

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.Schema({
      userId: mongoose.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.models.UserActivity ||
                        mongoose.model('UserActivity', UserActivitySchema);

    // Define the SystemLog schema directly
    const SystemLogSchema = new mongoose.Schema({
      level: { type: String, enum: ['info', 'warning', 'error', 'debug'], required: true },
      message: { type: String, required: true },
      source: { type: String, required: true },
      timestamp: { type: Date, default: Date.now },
      details: mongoose.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the SystemLog model
    const SystemLog = mongoose.models.SystemLog ||
                     mongoose.model('SystemLog', SystemLogSchema);

    // Parse additional query parameters
    const limit = parseInt(searchParams.get('limit') || '50');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    const level = searchParams.get('level') as 'info' | 'warning' | 'error' | 'debug' | undefined;
    const source = searchParams.get('source') || undefined;

    // Log this activity directly
    try {
      await UserActivity.create({
        userId: new mongoose.Types.ObjectId(userId),
        type: 'admin',
        action: 'view_logs',
        details: 'Viewed system logs',
        ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date()
      } as IUserActivity);
    } catch (activityError) {
      console.error('Error logging user activity:', activityError);
    }

    // Create sample logs in development if collection is empty
    if (process.env.NODE_ENV === 'development') {
      const count = await SystemLog.countDocuments();
      if (count === 0) {
        try {
          // Create some sample logs if none exist
          const sampleLogs = [
            {
              level: 'info',
              message: 'System startup complete',
              source: 'system',
              timestamp: new Date(),
              details: { uptime: '0s', environment: 'development' }
            },
            {
              level: 'warning',
              message: 'High memory usage detected',
              source: 'system',
              timestamp: new Date(Date.now() - 5 * 60 * 1000),
              details: { memoryUsage: '85%', available: '512MB' }
            },
            {
              level: 'error',
              message: 'Database connection failed',
              source: 'database',
              timestamp: new Date(Date.now() - 15 * 60 * 1000),
              details: { error: 'Connection timeout', retries: 3 }
            },
            {
              level: 'info',
              message: 'User authentication successful',
              source: 'auth',
              timestamp: new Date(Date.now() - 2 * 60 * 1000),
              details: { method: 'credentials' }
            },
            {
              level: 'debug',
              message: 'Config loaded from environment',
              source: 'server',
              timestamp: new Date(Date.now() - 30 * 60 * 1000),
              details: { variables: 12, source: '.env.local' }
            }
          ];
          
          await SystemLog.insertMany(sampleLogs);
          console.log('Created sample system logs for development');
        } catch (error) {
          console.error('Error creating sample logs:', error);
        }
      }
    }

    try {
      // Get system logs directly
      const query: Record<string, string> = {};
      if (level) query.level = level;
      if (source) query.source = source;

      // Get total count for pagination
      const total = await SystemLog.countDocuments(query);

      // Get logs with pagination
      const logs = await SystemLog.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      // Return logs with pagination info and cache headers
      return setCacheHeaders(
        NextResponse.json({
          logs: logs.map((log) => {
            // Use type assertion to convert mongoose document to our interface
            const typedLog = log as unknown as ISystemLogDocument;
            return {
              _id: typedLog._id.toString(),
              level: typedLog.level,
              message: typedLog.message,
              source: typedLog.source,
              timestamp: typedLog.timestamp,
              details: typedLog.details ? JSON.stringify(typedLog.details) : undefined
            };
          }),
          pagination: {
            total,
            limit,
            page,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (logError) {
      console.error('Error retrieving system logs:', logError);

      // Try to read logs from file system as fallback
      try {
        const logDir = path.join(process.cwd(), 'logs');
        const logFiles = await fs.readdir(logDir);
        const recentLogFile = logFiles
          .filter(file => file.endsWith('.log'))
          .sort()
          .reverse()[0];

        if (recentLogFile) {
          const logContent = await fs.readFile(path.join(logDir, recentLogFile), 'utf8');
          const logLines = logContent.split('\n').filter(Boolean).slice(-limit);

          const parsedLogs = logLines.map((line, index) => {
            try {
              const parsed = JSON.parse(line);
              return {
                _id: `file-${index}`,
                level: parsed.level || 'info',
                message: parsed.message || line,
                source: parsed.source || 'file',
                timestamp: parsed.timestamp || new Date().toISOString(),
                details: parsed
              };
            } catch (e) {
              return {
                _id: `file-${index}`,
                level: 'info',
                message: line,
                source: 'file',
                timestamp: new Date().toISOString()
              };
            }
          });

          return setCacheHeaders(
            NextResponse.json({
              logs: parsedLogs,
              pagination: {
                total: parsedLogs.length,
                limit,
                page: 1,
                pages: 1
              }
            })
          );
        }
      } catch (fsError) {
        console.error('Error reading log files:', fsError);
      }

      // No sample logs - only real data

      // Return empty logs array instead of mock data
      return setCacheHeaders(
        NextResponse.json({
          logs: [],
          pagination: {
            total: 0,
            limit,
            page: 1,
            pages: 1
          },
          error: 'Failed to retrieve logs'
        })
      );
    }
  } catch (error) {
    console.error('Unhandled error in system logs API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}



// Wrap the handler with MongoDB middleware
const GET = withMongo(getHandler);

export { GET };
